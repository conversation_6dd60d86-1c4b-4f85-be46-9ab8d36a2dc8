<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="24">
            <a-form-item label="客户端名称">
              <a-input v-model="queryParam.clientName" placeholder="请输入客户端名称" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="状态">
              <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="api_client_status"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('API客户端配置')">导出</a-button>
      <a-button type="primary" icon="upload" @click="handleImportExcel">导入</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        
        <template slot="statusSlot" slot-scope="text">
          <a-tag :color="text == 1 ? 'green' : 'red'">
            {{ text == 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template slot="apiSecretSlot" slot-scope="text">
          <span v-if="text">{{ text.substring(0, 8) }}****{{ text.substring(text.length - 8) }}</span>
        </template>

        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm :title="`确定要${record.status === 1 ? '禁用' : '启用'}吗？`" @confirm="() => handleToggleStatus(record)">
                  <a href="javascript:;">{{ record.status === 1 ? '禁用' : '启用' }}</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定要重新生成API密钥吗？原密钥将失效！" @confirm="() => handleRegenerateKeys(record)">
                  <a href="javascript:;">重新生成密钥</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>

      </a-table>
    </div>

    <api-client-modal ref="modalForm" @ok="modalFormOk"></api-client-modal>
  </a-card>
</template>

<script>
  import ApiClientModal from '@/views/comInterface/modules/ApiClientModal.vue'

  export default {
    name: 'ApiClientList',
    components: {
      ApiClientModal
    },
    data () {
      return {
        description: 'API客户端配置管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'客户端名称',
            align:"center",
            dataIndex: 'clientName'
          },
          {
            title:'API密钥',
            align:"center",
            dataIndex: 'apiKey'
          },
          {
            title:'API秘钥',
            align:"center",
            dataIndex: 'apiSecret',
            scopedSlots: {customRender: 'apiSecretSlot'}
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            scopedSlots: {customRender: 'statusSlot'}
          },
          {
            title:'过期时间',
            align:"center",
            dataIndex: 'expireTime'
          },
          {
            title:'IP白名单',
            align:"center",
            dataIndex: 'ipWhitelist'
          },
          {
            title:'创建时间',
            align:"center",
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/comInterface/apiClient/list",
          delete: "/comInterface/apiClient/delete",
          deleteBatch: "/comInterface/apiClient/deleteBatch",
          exportXlsUrl: "/comInterface/apiClient/exportXls",
          importExcelUrl: "comInterface/apiClient/importExcel",
          toggleStatus: "/comInterface/apiClient/toggleStatus",
          regenerateKeys: "/comInterface/apiClient/regenerateKeys"
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'clientName',text:'客户端名称',dictCode:''})
        fieldList.push({type:'string',value:'apiKey',text:'API密钥',dictCode:''})
        fieldList.push({type:'string',value:'apiSecret',text:'API秘钥',dictCode:''})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'api_client_status'})
        fieldList.push({type:'date',value:'expireTime',text:'过期时间',dictCode:''})
        fieldList.push({type:'string',value:'ipWhitelist',text:'IP白名单',dictCode:''})
        fieldList.push({type:'string',value:'remark',text:'备注',dictCode:''})
        this.superFieldList = fieldList
      },
      handleToggleStatus: function (record) {
        let that = this;
        let status = record.status === 1 ? 0 : 1;
        this.$http.post(this.url.toggleStatus, {id: record.id, status: status}).then((res) => {
          if (res.success) {
            that.$message.success(res.message);
            that.loadData();
          } else {
            that.$message.warning(res.message);
          }
        });
      },
      handleRegenerateKeys: function (record) {
        let that = this;
        this.$http.post(this.url.regenerateKeys, {id: record.id}).then((res) => {
          if (res.success) {
            that.$message.success(res.message);
            that.loadData();
          } else {
            that.$message.warning(res.message);
          }
        });
      }
    }
  }
</script>
<style scoped>

</style>
