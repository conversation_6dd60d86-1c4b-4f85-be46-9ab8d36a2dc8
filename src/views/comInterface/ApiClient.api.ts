import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/comInterface/apiClient/list',
  save = '/comInterface/apiClient/add',
  edit = '/comInterface/apiClient/edit',
  deleteOne = '/comInterface/apiClient/delete',
  deleteBatch = '/comInterface/apiClient/deleteBatch',
  queryById = '/comInterface/apiClient/queryById',
  exportXls = '/comInterface/apiClient/exportXls',
  importExcel = '/comInterface/apiClient/importExcel',
  toggleStatus = '/comInterface/apiClient/toggleStatus',
  regenerateKeys = '/comInterface/apiClient/regenerateKeys',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 根据id查询
 * @param id
 */
export const queryById = (id) => {
  return defHttp.get({ url: Api.queryById, params: { id } });
};

/**
 * 切换状态
 * @param params
 */
export const toggleStatus = (params) => {
  return defHttp.put({ url: Api.toggleStatus, params });
};

/**
 * 重新生成密钥
 * @param params
 */
export const regenerateKeys = (params) => {
  return defHttp.put({ url: Api.regenerateKeys, params });
};
