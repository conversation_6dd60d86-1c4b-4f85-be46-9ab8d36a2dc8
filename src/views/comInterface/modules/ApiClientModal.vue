<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
    
      <a-form ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="24">
            <a-form-item label="客户端名称" prop="clientName">
              <a-input v-model="model.clientName" placeholder="请输入客户端名称"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row v-if="!model.id">
          <a-col :span="24">
            <a-alert message="API密钥和秘钥将在保存后自动生成" type="info" show-icon style="margin-bottom: 16px"/>
          </a-col>
        </a-row>
        
        <a-row v-if="model.id">
          <a-col :span="24">
            <a-form-item label="API密钥">
              <a-input v-model="model.apiKey" :disabled="true">
                <a-icon slot="suffix" type="copy" @click="copyToClipboard(model.apiKey)" style="cursor: pointer"/>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row v-if="model.id">
          <a-col :span="24">
            <a-form-item label="API秘钥">
              <a-input-password v-model="model.apiSecret" :disabled="true">
                <a-icon slot="suffix" type="copy" @click="copyToClipboard(model.apiSecret)" style="cursor: pointer"/>
              </a-input-password>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="12">
            <a-form-item label="状态" prop="status">
              <j-dict-select-tag v-model="model.status" dictCode="api_client_status" placeholder="请选择状态"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="过期时间">
              <j-date picker="datetime" v-model="model.expireTime" placeholder="请选择过期时间" style="width: 100%"/>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24">
            <a-form-item label="IP白名单">
              <a-textarea v-model="model.ipWhitelist" placeholder="多个IP用逗号分隔，如：***********,***********" :rows="3"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24">
            <a-form-item label="备注">
              <a-textarea v-model="model.remark" placeholder="请输入备注信息" :rows="3"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'ApiClientModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          clientName: [
            { required: true, message: '请输入客户端名称!'},
          ],
          status: [
            { required: true, message: '请选择状态!'},
          ],
        },
        url: {
          add: "/comInterface/apiClient/add",
          edit: "/comInterface/apiClient/edit",
          queryById: "/comInterface/apiClient/queryById"
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(this.model)
        })
        //时间格式化
        if(this.model.expireTime){
          this.model.expireTime = this.model.expireTime
        }
        if(record.id){
          this.title = "编辑";
        }else{
          this.title = "新增";
          // 设置默认状态为启用
          this.model.status = 1;
        }
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model, values);
            //时间格式化
            if(formData.expireTime){
              formData.expireTime = formData.expireTime
            }
            console.log(formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      copyToClipboard(text) {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(text).then(() => {
            this.$message.success('已复制到剪贴板');
          }).catch(() => {
            this.fallbackCopyTextToClipboard(text);
          });
        } else {
          this.fallbackCopyTextToClipboard(text);
        }
      },
      fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
          this.$message.success('已复制到剪贴板');
        } catch (err) {
          this.$message.error('复制失败，请手动复制');
        }
        document.body.removeChild(textArea);
      }
    }
  }
</script>
